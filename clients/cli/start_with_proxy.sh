#!/bin/bash

# 快速启动脚本 - 使用您的代理配置
# 使用方法: ./start_with_proxy.sh

echo "🚀 启动Nexus多节点代理模式"
echo "================================"

# 您的代理URL
PROXY_URL="http://brd-customer-hl_75cad719-zone-data_center-ip-***************:<EMAIL>:33335"

# 创建临时配置文件
CONFIG_FILE="temp_nodes.csv"

echo "📝 创建节点配置文件..."
cat > $CONFIG_FILE << EOF
# 临时节点配置
# Format: proxy_url,node_id

$PROXY_URL,12345
$PROXY_URL,12346
EOF

echo "✅ 配置文件已创建: $CONFIG_FILE"
echo ""

echo "🔨 编译最新版本..."
cargo build --release

if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    exit 1
fi

echo "✅ 编译成功！"
echo ""

echo "🌐 启动多节点（按Ctrl+C停止）..."
echo "预期看到:"
echo "  🚀 Starting X nodes concurrently..."
echo "  🌐 Node proxy configured: ..."
echo "  ✅ All X nodes started successfully!"
echo ""

# 启动多节点
./target/release/nexus-network start \
  --config $CONFIG_FILE \
  --headless \
  --max-threads 1 \
  --skip-resource-check

# 清理临时文件
echo ""
echo "🧹 清理临时文件..."
rm -f $CONFIG_FILE

echo "✅ 完成！"
