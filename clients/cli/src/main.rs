// Copyright (c) 2024 Nexus. All rights reserved.

mod analytics;
mod config;
mod consts;
mod environment;
mod error_classifier;
mod events;
mod keys;
mod logging;
#[path = "proto/nexus.orchestrator.rs"]
mod nexus_orchestrator;
mod orchestrator;
mod pretty;
mod prover;
mod prover_runtime;
mod register;
pub mod system;
mod task;
mod task_cache;
mod ui;
mod version_checker;
mod version_requirements;
mod workers;

use crate::config::{Config, get_config_path};
use crate::environment::Environment;
use crate::events::{Event, EventType};
use crate::orchestrator::{Orchestrator, OrchestratorClient};
use crate::pretty::print_cmd_info;
use crate::prover_runtime::{start_anonymous_workers, start_authenticated_workers};
use crate::register::{register_node, register_user};
use crate::system::{num_cores, get_memory_info, total_memory_gb};
use crate::version_requirements::{VersionRequirements, VersionRequirementsError};
use clap::{ArgAction, Parser, Subcommand};
use crossterm::{
    event::{DisableMouseCapture, EnableMouseCapture},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use ed25519_dalek::SigningKey;
use ratatui::{Terminal, backend::CrosstermBackend};
use std::{error::Error, io, fs};
use tokio::sync::broadcast;
use std::time::{Duration, Instant};

/// Configuration for a single node from CSV
#[derive(Debug, Clone)]
struct NodeConfig {
    proxy_url: String,
    node_id: u64,
}

/// Resource management configuration
#[derive(Debug, Clone)]
struct ResourceConfig {
    total_nodes: usize,
    threads_per_node: usize,
    total_threads: usize,
    available_cores: usize,
    total_memory_gb: f64,
    memory_per_node_gb: f64,
}

impl ResourceConfig {
    fn new(num_nodes: usize, max_threads: Option<u32>) -> Self {
        let available_cores = num_cores();
        let total_memory_gb = total_memory_gb();

        // Calculate optimal threads per node
        let threads_per_node = Self::calculate_optimal_threads_per_node(
            num_nodes,
            max_threads,
            available_cores,
        );

        let total_threads = threads_per_node * num_nodes;
        let memory_per_node_gb = total_memory_gb / num_nodes as f64;

        ResourceConfig {
            total_nodes: num_nodes,
            threads_per_node,
            total_threads,
            available_cores,
            total_memory_gb,
            memory_per_node_gb,
        }
    }

    fn calculate_optimal_threads_per_node(
        num_nodes: usize,
        max_threads: Option<u32>,
        available_cores: usize,
    ) -> usize {
        // 简化逻辑：基于实际计算需求
        // 每个任务需要2核心，所以最大并发任务数 = 核心数 / 2
        let max_concurrent_tasks = std::cmp::max(1, available_cores / 2);

        // 如果用户指定了线程数，使用用户设置（但不超过系统限制）
        if let Some(user_threads) = max_threads {
            return std::cmp::min(user_threads as usize, max_concurrent_tasks);
        }

        // 自动计算：每个节点最多能同时处理多少任务
        let threads_per_node = std::cmp::max(1, max_concurrent_tasks / num_nodes);

        // 限制最大值，避免过度分配
        std::cmp::min(threads_per_node, 4)
    }

    fn validate_system_resources(&self) -> Result<(), String> {
        // 轻量级验证：只提供信息性警告，不阻止启动

        // 连接数警告（大量节点时）
        if self.total_nodes > 1000 {
            eprintln!(
                "ℹ️  大规模部署: {} 个节点 - 请确保系统连接限制足够\n\
                检查: ulimit -n 和 /proc/sys/fs/file-max",
                self.total_nodes
            );
        }

        // 内存使用提示（仅信息性）
        if self.total_nodes > 100 {
            eprintln!(
                "ℹ️  节点规模: {} 个节点将主要进行I/O等待，内存使用较低\n\
                实际计算时每个任务约需2GB内存",
                self.total_nodes
            );
        }

        Ok(())
    }

    fn print_resource_summary(&self) {
        print_cmd_info!(
            "Resource Allocation Summary",
            "Nodes: {}, Threads/Node: {}, Total Threads: {}",
            self.total_nodes,
            self.threads_per_node,
            self.total_threads
        );

        if self.total_nodes >= 100 {
            // High-concurrency display
            print_cmd_info!(
                "System Resources (High-Concurrency Mode)",
                "CPU Cores: {}, Memory: {:.1}GB ({:.0}MB per node)",
                self.available_cores,
                self.total_memory_gb,
                self.memory_per_node_gb * 1024.0
            );

            // High-concurrency recommendations
            let memory_per_node_mb = self.memory_per_node_gb * 1024.0;
            if memory_per_node_mb < 50.0 {
                eprintln!("⚠️  Very low memory per node ({:.0}MB). Monitor for memory pressure.", memory_per_node_mb);
            } else if memory_per_node_mb > 200.0 {
                eprintln!("ℹ️  Generous memory allocation ({:.0}MB per node). You could potentially run more nodes.", memory_per_node_mb);
            } else {
                eprintln!("✅ Good memory allocation for I/O-bound workload ({:.0}MB per node).", memory_per_node_mb);
            }

            // Connection limit info
            if self.total_nodes > 500 {
                eprintln!("ℹ️  Large-scale deployment: Monitor system limits (ulimit -n, file descriptors)");
            }

        } else {
            // Standard display
            print_cmd_info!(
                "System Resources",
                "CPU Cores: {}, Memory: {:.1}GB ({:.1}GB per node)",
                self.available_cores,
                self.total_memory_gb,
                self.memory_per_node_gb
            );

            // Standard recommendations
            let cpu_utilization = (self.total_threads as f64 / self.available_cores as f64) * 100.0;
            if cpu_utilization > 200.0 {
                eprintln!("⚠️  High thread-to-core ratio ({:.0}%). Acceptable for I/O-bound workloads.", cpu_utilization);
            } else if cpu_utilization < 25.0 {
                eprintln!("ℹ️  Low CPU utilization ({:.0}%). You could potentially run more nodes.", cpu_utilization);
            }
        }
    }
}

/// 判断是否应该记录此事件（减少日志输出）
fn should_log_event(event: &Event) -> bool {
    match event.event_type {
        EventType::Success => {
            // 只记录重要的成功事件
            event.msg.contains("Proof submitted") ||
            event.msg.contains("Performance Status")
        },
        EventType::Error => {
            // 过滤掉频繁的速率限制错误，只保留其他错误
            !event.msg.contains("Rate limited - retrying in 0s") &&
            !event.msg.contains("Rate limited - retrying in 1s") &&
            !event.msg.contains("Rate limited - retrying in 2s")
        },
        EventType::Refresh => {
            // 严格过滤，只保留真正重要的信息
            event.msg.contains("Performance Status") ||
            event.msg.contains("Resource Status") ||
            (event.msg.contains("rate limited") && event.msg.contains("retry") &&
             !event.msg.contains("retrying in 0s") &&
             !event.msg.contains("retrying in 1s") &&
             !event.msg.contains("retrying in 2s") &&
             !event.msg.contains("Every node in the Prover Network") &&
             !event.msg.contains("429 Rate limit retry-after") &&
             !event.msg.contains("Fetching tasks"))
        },
        EventType::Shutdown => true, // 关闭事件都记录
    }
}

/// Parse CSV configuration file with format: proxy_url,node_id
fn parse_csv_config(file_path: &str) -> Result<Vec<NodeConfig>, Box<dyn Error>> {
    let content = fs::read_to_string(file_path)?;
    let mut configs = Vec::new();

    for (line_num, line) in content.lines().enumerate() {
        let line = line.trim();
        if line.is_empty() || line.starts_with('#') {
            continue; // Skip empty lines and comments
        }

        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() != 2 {
            return Err(format!("Invalid CSV format at line {}: expected 'proxy_url,node_id'", line_num + 1).into());
        }

        let proxy_url = parts[0].trim().to_string();
        let node_id = parts[1].trim().parse::<u64>()
            .map_err(|_| format!("Invalid node ID at line {}: '{}'", line_num + 1, parts[1]))?;

        configs.push(NodeConfig { proxy_url, node_id });
    }

    if configs.is_empty() {
        return Err("No valid node configurations found in CSV file".into());
    }

    Ok(configs)
}

#[derive(Parser)]
#[command(author, version, about, long_about = None)]
/// Command-line arguments
struct Args {
    /// Command to execute
    #[command(subcommand)]
    command: Command,
}

#[derive(Subcommand)]
enum Command {
    /// Start the prover
    Start {
        /// Node ID
        #[arg(long, value_name = "NODE_ID")]
        node_id: Option<u64>,

        /// CSV configuration file with format: proxy_url,node_id
        #[arg(long = "config", value_name = "CONFIG_FILE")]
        config_file: Option<String>,

        /// Run without the terminal UI
        #[arg(long = "headless", action = ArgAction::SetTrue)]
        headless: bool,

        /// Maximum number of threads to use for proving per node.
        #[arg(long = "max-threads", value_name = "MAX_THREADS")]
        max_threads: Option<u32>,

        /// Skip resource validation warnings
        #[arg(long = "skip-resource-check", action = ArgAction::SetTrue)]
        skip_resource_check: bool,

        /// Custom orchestrator URL (overrides environment setting)
        #[arg(long = "orchestrator-url", value_name = "URL")]
        orchestrator_url: Option<String>,

        /// Disable background colors in the dashboard
        #[arg(long = "no-background-color", action = ArgAction::SetTrue)]
        no_background_color: bool,
    },
    /// Register a new user
    RegisterUser {
        /// User's public Ethereum wallet address. 42-character hex string starting with '0x'
        #[arg(long, value_name = "WALLET_ADDRESS")]
        wallet_address: String,
    },
    /// Register a new node to an existing user, or link an existing node to a user.
    RegisterNode {
        /// ID of the node to register. If not provided, a new node will be created.
        #[arg(long, value_name = "NODE_ID")]
        node_id: Option<u64>,
    },
    /// Clear the node configuration and logout.
    Logout,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    let nexus_environment_str = std::env::var("NEXUS_ENVIRONMENT").unwrap_or_default();
    let environment = nexus_environment_str
        .parse::<Environment>()
        .unwrap_or(Environment::default());

    let config_path = get_config_path()?;

    let args = Args::parse();
    match args.command {
        Command::Start {
            node_id,
            config_file,
            headless,
            max_threads,
            skip_resource_check,
            orchestrator_url,
            no_background_color,
        } => {
            // If a custom orchestrator URL is provided, create a custom environment
            let final_environment = if let Some(url) = orchestrator_url {
                Environment::Custom {
                    orchestrator_url: url,
                }
            } else {
                environment
            };
            start(
                node_id,
                config_file,
                final_environment,
                config_path,
                headless,
                max_threads,
                skip_resource_check,
                no_background_color,
            )
            .await
        }
        Command::Logout => {
            print_cmd_info!("Logging out", "Clearing node configuration file...");
            Config::clear_node_config(&config_path).map_err(Into::into)
        }
        Command::RegisterUser { wallet_address } => {
            print_cmd_info!("Registering user", "Wallet address: {}", wallet_address);
            let orchestrator = Box::new(OrchestratorClient::new(environment));
            register_user(&wallet_address, &config_path, orchestrator).await
        }
        Command::RegisterNode { node_id } => {
            let orchestrator = Box::new(OrchestratorClient::new(environment));
            register_node(node_id, &config_path, orchestrator).await
        }
    }
}

/// Starts the Nexus CLI application.
///
/// # Arguments
/// * `node_id` - This client's unique identifier, if available.
/// * `config_file` - Optional CSV configuration file path.
/// * `env` - The environment to connect to.
/// * `config_path` - Path to the configuration file.
/// * `headless` - If true, runs without the terminal UI.
/// * `max_threads` - Optional maximum number of threads to use for proving.
/// * `conservative` - Enable conservative resource usage.
/// * `skip_resource_check` - Skip resource validation warnings.
/// * `high_concurrency` - Enable high-concurrency mode for 100+ nodes.
async fn start(
    node_id: Option<u64>,
    config_file: Option<String>,
    env: Environment,
    config_path: std::path::PathBuf,
    headless: bool,
    max_threads: Option<u32>,
    skip_resource_check: bool,
    no_background_color: bool,
) -> Result<(), Box<dyn Error>> {
    // Handle multi-node configuration from CSV file
    if let Some(csv_path) = config_file {
        if node_id.is_some() {
            return Err("Cannot specify both --node-id and --config options simultaneously".into());
        }

        let node_configs = parse_csv_config(&csv_path)?;
        print_cmd_info!("Multi-node mode", "Starting {} nodes from {}", node_configs.len(), csv_path);

        return start_multi_node(node_configs, env, headless, max_threads, skip_resource_check, no_background_color).await;
    }

    // Check version requirements before starting any workers
    match VersionRequirements::fetch().await {
        Ok(requirements) => {
            let current_version = env!("CARGO_PKG_VERSION");
            match requirements.check_version_constraints(current_version, None, None) {
                Ok(Some(violation)) => match violation.constraint_type {
                    crate::version_requirements::ConstraintType::Blocking => {
                        eprintln!("❌ Version requirement not met: {}", violation.message);
                        std::process::exit(1);
                    }
                    crate::version_requirements::ConstraintType::Warning => {
                        eprintln!("⚠️  {}", violation.message);
                    }
                    crate::version_requirements::ConstraintType::Notice => {
                        eprintln!("ℹ️  {}", violation.message);
                    }
                },
                Ok(None) => {
                    // No violations found, continue
                }
                Err(e) => {
                    eprintln!("❌ Failed to parse version requirements: {}", e);
                    eprintln!(
                        "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues"
                    );
                    std::process::exit(1);
                }
            }
        }
        Err(VersionRequirementsError::Fetch(e)) => {
            eprintln!("❌ Failed to fetch version requirements: {}", e);
            eprintln!(
                "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues"
            );
            std::process::exit(1);
        }
        Err(e) => {
            eprintln!("❌ Failed to check version requirements: {}", e);
            eprintln!(
                "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues"
            );
            std::process::exit(1);
        }
    }

    let mut node_id = node_id;

    // If no node ID is provided, try to load it from the config file.
    if node_id.is_none() && config_path.exists() {
        let config = Config::load_from_file(&config_path)?;

        // Check if user is registered but node_id is missing or invalid
        if !config.user_id.is_empty() {
            if config.node_id.is_empty() {
                print_cmd_info!(
                    "✅ User registered, but no node found.",
                    "Please register a node to continue: nexus-cli register-node"
                );
                return Err(
                    "Node registration required. Please run 'nexus-cli register-node' first."
                        .into(),
                );
            }

            match config.node_id.parse::<u64>() {
                Ok(id) => {
                    node_id = Some(id);
                    print_cmd_info!("✅ Found Node ID from config file", "Node ID: {}", id);
                }
                Err(_) => {
                    print_cmd_info!(
                        "❌ Invalid node ID in config file.",
                        "Please register a new node: nexus-cli register-node"
                    );
                    return Err("Invalid node ID in config. Please run 'nexus-cli register-node' to fix this.".into());
                }
            }
        } else {
            print_cmd_info!(
                "❌ No user registration found.",
                "Please register your wallet address first: nexus-cli register-user --wallet-address <your-wallet-address>"
            );
            return Err("User registration required. Please run 'nexus-cli register-user --wallet-address <your-wallet-address>' first.".into());
        }
    } else if node_id.is_none() {
        // No config file exists at all
        print_cmd_info!(
            "Welcome to Nexus CLI!",
            "Please register your wallet address to get started: nexus-cli register-user --wallet-address <your-wallet-address>"
        );
    }

    // Create a signing key for the prover.
    let mut csprng = rand_core::OsRng;
    let signing_key: SigningKey = SigningKey::generate(&mut csprng);
    let orchestrator_client = OrchestratorClient::new(env.clone());
    // Clamp the number of workers to [1,8]. Keep this low for now to avoid rate limiting.
    let num_workers: usize = max_threads.unwrap_or(1).clamp(1, 8) as usize;
    let (shutdown_sender, _) = broadcast::channel(1); // Only one shutdown signal needed

    // Get client_id for analytics - use wallet address from API if available, otherwise "anonymous"
    let client_id = if let Some(node_id) = node_id {
        match orchestrator_client.get_node(&node_id.to_string()).await {
            Ok(wallet_address) => {
                // Use wallet address as client_id for analytics
                wallet_address
            }
            Err(_) => {
                // If API call fails, use "anonymous" regardless of config
                "anonymous".to_string()
            }
        }
    } else {
        // No node_id available, use "anonymous"
        "anonymous".to_string()
    };

    let (mut event_receiver, mut join_handles) = match node_id {
        Some(node_id) => {
            start_authenticated_workers(
                node_id,
                signing_key.clone(),
                orchestrator_client.clone(),
                num_workers,
                shutdown_sender.subscribe(),
                env.clone(),
                client_id,
            )
            .await
        }
        None => {
            start_anonymous_workers(num_workers, shutdown_sender.subscribe(), env, client_id).await
        }
    };

    if !headless {
        // Terminal setup
        enable_raw_mode()?;
        let mut stdout = io::stdout();
        execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;

        // Initialize the terminal with Crossterm backend.
        let backend = CrosstermBackend::new(stdout);
        let mut terminal = Terminal::new(backend)?;

        // Create the application and run it.
        let app = ui::App::new(
            node_id,
            orchestrator_client.environment().clone(),
            event_receiver,
            shutdown_sender,
            no_background_color,
        );
        let res = ui::run(&mut terminal, app).await;

        // Clean up the terminal after running the application.
        disable_raw_mode()?;
        execute!(
            terminal.backend_mut(),
            LeaveAlternateScreen,
            DisableMouseCapture
        )?;
        terminal.show_cursor()?;

        res?;
    } else {
        // Headless mode: log events to console.

        // Trigger shutdown on Ctrl+C
        let shutdown_sender_clone = shutdown_sender.clone();
        tokio::spawn(async move {
            if tokio::signal::ctrl_c().await.is_ok() {
                let _ = shutdown_sender_clone.send(());
            }
        });

        let mut shutdown_receiver = shutdown_sender.subscribe();
        loop {
            tokio::select! {
                Some(event) = event_receiver.recv() => {
                    println!("{}", event);
                }
                _ = shutdown_receiver.recv() => {
                    break;
                }
            }
        }
    }
    println!("\nExiting...");
    for handle in join_handles.drain(..) {
        let _ = handle.await;
    }
    println!("Nexus CLI application exited successfully.");
    Ok(())
}

/// Starts multiple nodes concurrently based on CSV configuration
async fn start_multi_node(
    node_configs: Vec<NodeConfig>,
    _env: Environment,
    headless: bool,
    max_threads: Option<u32>,
    skip_resource_check: bool,
    _no_background_color: bool,
) -> Result<(), Box<dyn Error>> {
    // Initialize resource management
    let resource_config = ResourceConfig::new(node_configs.len(), max_threads);

    // Validate system resources before starting (unless skipped)
    if !skip_resource_check {
        if let Err(warning) = resource_config.validate_system_resources() {
            eprintln!("{}", warning);
        }
    }

    // Print resource allocation summary
    resource_config.print_resource_summary();
    // Check version requirements before starting any workers
    match VersionRequirements::fetch().await {
        Ok(requirements) => {
            let current_version = env!("CARGO_PKG_VERSION");
            match requirements.check_version_constraints(current_version, None, None) {
                Ok(Some(violation)) => match violation.constraint_type {
                    crate::version_requirements::ConstraintType::Blocking => {
                        eprintln!("❌ Version requirement not met: {}", violation.message);
                        std::process::exit(1);
                    }
                    crate::version_requirements::ConstraintType::Warning => {
                        eprintln!("⚠️  {}", violation.message);
                    }
                    crate::version_requirements::ConstraintType::Notice => {
                        eprintln!("ℹ️  {}", violation.message);
                    }
                },
                Ok(None) => {
                    // No violations found, continue
                }
                Err(e) => {
                    eprintln!("❌ Failed to parse version requirements: {}", e);
                    eprintln!(
                        "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues"
                    );
                    std::process::exit(1);
                }
            }
        }
        Err(VersionRequirementsError::Fetch(e)) => {
            eprintln!("❌ Failed to fetch version requirements: {}", e);
            eprintln!(
                "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues"
            );
            std::process::exit(1);
        }
        Err(e) => {
            eprintln!("❌ Failed to check version requirements: {}", e);
            eprintln!(
                "If this issue persists, please file a bug report at: https://github.com/nexus-xyz/nexus-cli/issues"
            );
            std::process::exit(1);
        }
    }

    let (shutdown_sender, _) = broadcast::channel(1);
    let mut all_join_handles = Vec::new();
    let mut all_event_receivers = Vec::new();

    // 并发启动所有节点
    println!("🚀 Starting {} nodes concurrently...", node_configs.len());

    let mut node_tasks = Vec::new();

    // 为每个节点创建启动任务
    for (index, node_config) in node_configs.iter().enumerate() {
        let node_config = node_config.clone();
        let env = _env.clone();
        let shutdown_sender = shutdown_sender.clone();
        let threads_per_node = resource_config.threads_per_node;

        let task = tokio::spawn(async move {
            print_cmd_info!(
                "Starting node {}",
                "Node ID: {}, Proxy URL: {}",
                node_config.node_id,
                node_config.proxy_url
            );



            // Create a signing key for the prover
            let mut csprng = rand_core::OsRng;
            let signing_key: SigningKey = SigningKey::generate(&mut csprng);
            let orchestrator_client = OrchestratorClient::new_with_proxy(
                env.clone(),
                Some(node_config.proxy_url.clone())
            );

            // 验证节点是否存在并获取client_id
            let client_id = match orchestrator_client.get_node(&node_config.node_id.to_string()).await {
                Ok(wallet_address) => {
                    println!("✅ Node {} verified: {}", node_config.node_id, wallet_address);
                    wallet_address
                },
                Err(e) => {
                    eprintln!("❌ Node {} verification failed: {}", node_config.node_id, e);
                    eprintln!("   This node may not be registered or accessible through the proxy");
                    format!("node_{}", node_config.node_id)
                },
            };

            let (event_receiver, join_handles) = start_authenticated_workers(
                node_config.node_id,
                signing_key.clone(),
                orchestrator_client.clone(),
                threads_per_node,
                shutdown_sender.subscribe(),
                env.clone(),
                client_id,
            )
            .await;

            (index, event_receiver, join_handles)
        });

        node_tasks.push(task);
    }

    // 等待所有节点启动完成
    println!("⏳ Waiting for all nodes to initialize...");
    for task in node_tasks {
        match task.await {
            Ok((index, event_receiver, join_handles)) => {
                all_event_receivers.push((index, event_receiver));
                all_join_handles.extend(join_handles);
            }
            Err(e) => {
                eprintln!("❌ Failed to start node: {}", e);
            }
        }
    }

    println!("✅ All {} nodes started successfully!", node_configs.len());

    if !headless {
        // For multi-node mode, we'll use headless mode for now
        // TODO: Implement multi-node UI support
        eprintln!("⚠️  Multi-node mode currently only supports headless operation");
        eprintln!("   Running in headless mode...");
    }

    // Headless mode: log events from all nodes to console
    let shutdown_sender_clone = shutdown_sender.clone();
    tokio::spawn(async move {
        if tokio::signal::ctrl_c().await.is_ok() {
            let _ = shutdown_sender_clone.send(());
        }
    });

    let mut shutdown_receiver = shutdown_sender.subscribe();

    // Spawn tasks to handle events from each node with filtering
    for (node_index, mut event_receiver) in all_event_receivers {
        let node_id = node_configs[node_index].node_id;
        tokio::spawn(async move {
            while let Some(event) = event_receiver.recv().await {
                // 过滤日志：只显示重要事件，减少冗余输出
                if should_log_event(&event) {
                    println!("[Node {}] {}", node_id, event);
                }
            }
        });
    }

    // Start resource monitoring task
    let resource_monitor_handle = start_resource_monitor(
        resource_config.clone(),
        shutdown_sender.subscribe()
    );
    all_join_handles.push(resource_monitor_handle);

    // Wait for shutdown signal
    let _ = shutdown_receiver.recv().await;

    println!("\nExiting multi-node mode...");
    for handle in all_join_handles {
        let _ = handle.await;
    }
    println!("All nodes exited successfully.");
    Ok(())
}

/// Starts a resource monitoring task that periodically reports system resource usage
fn start_resource_monitor(
    resource_config: ResourceConfig,
    mut shutdown: broadcast::Receiver<()>,
) -> tokio::task::JoinHandle<()> {
    tokio::spawn(async move {
        let mut last_report = Instant::now();
        let report_interval = Duration::from_secs(600); // Report every 10 minutes for large deployments
        let mut warning_count = 0;

        loop {
            tokio::select! {
                _ = shutdown.recv() => break,
                _ = tokio::time::sleep(Duration::from_secs(30)) => {
                    // Check system resources every 30 seconds
                    let (current_memory_mb, total_memory_mb) = get_memory_info();
                    let memory_usage_percent = (current_memory_mb as f64 / total_memory_mb as f64) * 100.0;

                    // Issue warnings for high resource usage
                    if memory_usage_percent > 85.0 {
                        warning_count += 1;
                        eprintln!(
                            "⚠️  High Memory Usage: {:.1}% ({} MB / {} MB) - Warning #{}",
                            memory_usage_percent, current_memory_mb, total_memory_mb, warning_count
                        );

                        if warning_count >= 3 {
                            eprintln!("❌ Critical: Memory usage consistently high. Consider reducing nodes or restarting.");
                        }
                    } else if memory_usage_percent < 70.0 {
                        // Reset warning count if memory usage drops
                        warning_count = 0;
                    }

                    // Periodic resource report
                    if last_report.elapsed() >= report_interval {
                        print_resource_status(&resource_config, current_memory_mb, total_memory_mb);
                        last_report = Instant::now();
                    }
                }
            }
        }
    })
}

/// Print current resource status
fn print_resource_status(
    resource_config: &ResourceConfig,
    current_memory_mb: i32,
    total_memory_mb: i32,
) {
    let memory_usage_percent = (current_memory_mb as f64 / total_memory_mb as f64) * 100.0;
    let memory_per_node_mb = current_memory_mb as f64 / resource_config.total_nodes as f64;

    println!("📊 Resource Status Report:");
    println!("   • Nodes: {} active", resource_config.total_nodes);
    println!("   • Threads: {} total ({} per node)", resource_config.total_threads, resource_config.threads_per_node);
    println!("   • Memory: {:.1}% used ({} MB / {} MB)", memory_usage_percent, current_memory_mb, total_memory_mb);
    println!("   • Memory per node: {:.1} MB", memory_per_node_mb);

    // Performance recommendations
    if memory_usage_percent > 80.0 {
        println!("   ⚠️  Consider reducing the number of nodes to improve performance");
    } else if memory_usage_percent < 50.0 {
        println!("   ℹ️  System resources are underutilized - you could potentially run more nodes");
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_resource_config_basic() {
        let config = ResourceConfig::new(4, None);
        assert_eq!(config.total_nodes, 4);
        assert!(config.threads_per_node >= 1);
        assert_eq!(config.total_threads, config.threads_per_node * 4);
    }

    #[test]
    fn test_resource_config_with_user_threads() {
        let config = ResourceConfig::new(2, Some(3));
        assert_eq!(config.threads_per_node, 3);
        assert_eq!(config.total_threads, 6);
    }

    #[test]
    fn test_resource_config_thread_clamping() {
        // Test that threads are clamped based on system resources
        let config_high = ResourceConfig::new(1, Some(20));
        // Should be limited by system resources (cores/2)
        assert!(config_high.threads_per_node >= 1);
    }

    #[test]
    fn test_resource_config_many_nodes() {
        // Test with many nodes - should allocate fewer threads per node
        let config = ResourceConfig::new(100, None);
        assert_eq!(config.total_nodes, 100);
        assert!(config.threads_per_node >= 1);
        assert_eq!(config.total_threads, config.threads_per_node * 100);
    }

    #[test]
    fn test_resource_config_memory_calculation() {
        // Test memory calculation
        let config = ResourceConfig::new(10, None);
        assert!(config.memory_per_node_gb > 0.0);
        assert_eq!(config.total_memory_gb, config.memory_per_node_gb * 10.0);
    }

    #[test]
    fn test_csv_parsing() {
        // Test parsing CSV content directly
        let csv_lines = vec![
            "http://proxy1.example.com,12345".to_string(),
            "http://proxy2.example.com,67890".to_string(),
        ];

        let mut configs = Vec::new();
        for line in csv_lines {
            if let Some(config) = parse_csv_line(&line) {
                configs.push(config);
            }
        }

        assert_eq!(configs.len(), 2);
        assert_eq!(configs[0].proxy_url, "http://proxy1.example.com");
        assert_eq!(configs[0].node_id, 12345);
        assert_eq!(configs[1].proxy_url, "http://proxy2.example.com");
        assert_eq!(configs[1].node_id, 67890);
    }

    // Helper function for parsing a single CSV line
    fn parse_csv_line(line: &str) -> Option<NodeConfig> {
        let line = line.trim();
        if line.is_empty() || line.starts_with('#') {
            return None;
        }

        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() != 2 {
            return None;
        }

        let proxy_url = parts[0].trim().to_string();
        let node_id = parts[1].trim().parse::<u64>().ok()?;

        Some(NodeConfig { proxy_url, node_id })
    }
}
