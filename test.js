/**
 * Orca交易自动化脚本 - 浏览器开发者工具版本
 *
 * 使用说明：
 * 1. 确保已安装Phantom钱包扩展
 * 2. 在Orca网站上打开开发者工具
 * 3. 将此代码粘贴到控制台中运行
 * 4. 脚本会自动处理交易签名和广播
 *
 * 注意事项：
 * - 请确保钱包中有足够的SOL支付交易费用
 * - 交易会自动等待确认，请耐心等待
 * - 如遇到问题，请检查钱包连接状态
 */

async function executeOrcaTrade() {
    const siteKey = '6LfI-l8rAAAAANFOE5niDrO8PEvuqHfvddN-4SNw';
    // 钱包地址
    const trader = "Bk12DihjYXEyT64899d3ZMEUNPQ2YWngbNsL2KzyFVo2";
    // 买的USDC 数量 6位小数
    let amountIn = 10
    amountIn += "00000";

    // token 地址
    const address = "TUNAfXDZEdQizTMTh3uEvNvYqJmqFHZbEJt8joP4cyx";

    // 1. 获取recahptcha token
    let token;
    try {
        token = await grecaptcha.enterprise.execute(siteKey, { action: 'buy' });
        console.log("✅ Captcha token (rresp):", token);
    } catch (e) {
        console.error("❌ 获取 reCAPTCHA token 失败:", e);
        return;
    }

    // 2. 发起 Orca 交易请求
    const body = {
        trader,
        address,
        token,
        side: "buy",
        args: {
            type: "exactIn",
            amountIn,
            allowPartialFill: true,
            priceThreshold: {
                numerator: 100000000,
                denominator: "2671627902570772"
            }
        },
        feeConfig: {
            priorityFee: {
                type: "dynamic",
                parameters: {
                    percentile: 50,
                    maxLamports: 9996000
                }
            },
            jito: {
                type: "dynamic",
                parameters: {
                    percentile: 50,
                    maxLamports: 4000
                }
            }
        }
    };

    try {
        const response = await fetch("https://api.orca.so/v2/solana/wavebreak/trades", {
            method: "POST",
            headers: {
                accept: "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "cache-control": "no-cache",
                "content-type": "application/json",
                pragma: "no-cache",
                priority: "u=1, i",
                "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site"
            },
            referrer: "https://www.orca.so/",
            body: JSON.stringify(body),
            mode: "cors",
            credentials: "omit"
        });

        const data = await response.json();
        // {"data":{"serializedTransaction": []}}
        console.log("🎯 Orca 返回结果:", data);

        // 处理返回的序列化交易
        if (data.data && data.data.serializedTransaction && data.data.serializedTransaction.length > 0) {
            console.log("📝 开始处理交易签名和广播...");
            const success = await signAndBroadcastTransaction(data.data.serializedTransaction);
            return success;
        } else {
            console.log("⚠️ 没有返回有效的交易数据");
            return false;
        }
    } catch (err) {
        console.error("❌ 请求失败:", err);
        return false;
    }
}

// 在浏览器环境中签名和广播交易
async function signAndBroadcastTransaction(serializedTransactions) {
    let allSuccessful = true;

    try {
        // 检查是否有Solana钱包
        if (!window.solana || !window.solana.isPhantom) {
            console.error("❌ 未检测到Phantom钱包，请安装Phantom钱包扩展");
            return false;
        }

        // 连接钱包
        const wallet = window.solana;
        if (!wallet.isConnected) {
            console.log("🔗 正在连接钱包...");
            await wallet.connect();
        }

        console.log("✅ 钱包已连接:", wallet.publicKey.toString());

        // 处理每个交易
        for (let i = 0; i < serializedTransactions.length; i++) {
            const serializedTx = serializedTransactions[i];
            console.log(`📤 处理第 ${i + 1}/${serializedTransactions.length} 个交易...`);

            try {
                // 使用钱包签名并发送交易
                const response = await wallet.signAndSendTransaction({
                    message: serializedTx
                });

                console.log("✅ 交易已签名并发送");
                const signature = response.signature;

                if (signature) {
                    console.log(`✅ 交易 ${i + 1} 发送成功!`);
                    console.log(`🔗 交易签名: ${signature}`);
                    console.log(`🌐 查看交易: https://solscan.io/tx/${signature}`);

                    // 等待交易确认
                    const confirmed = await waitForTransactionConfirmation(signature);
                    if (!confirmed) {
                        allSuccessful = false;
                    }
                } else {
                    console.error(`❌ 交易 ${i + 1} 发送失败`);
                    allSuccessful = false;
                }

            } catch (txError) {
                console.error(`❌ 处理交易 ${i + 1} 时出错:`, txError);
                allSuccessful = false;
            }
        }

        return allSuccessful;

    } catch (error) {
        console.error("❌ 签名交易失败:", error);

        if (error.message.includes('User rejected')) {
            console.log("ℹ️ 用户取消了交易签名");
        } else if (error.code === 4001) {
            console.log("ℹ️ 用户拒绝了连接请求");
        }

        return false;
    }
}

// 等待交易确认
async function waitForTransactionConfirmation(signature, maxAttempts = 30) {
    console.log("⏳ 等待交易确认...");

    for (let i = 0; i < maxAttempts; i++) {
        const status = await checkTransactionStatus(signature);

        if (status && status.value && status.value[0]) {
            const txStatus = status.value[0];

            if (txStatus.confirmationStatus === 'confirmed' || txStatus.confirmationStatus === 'finalized') {
                console.log("✅ 交易已确认!");
                return true;
            } else if (txStatus.err) {
                console.error("❌ 交易失败:", txStatus.err);
                return false;
            }
        }

        // 等待2秒后重试
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log(`⏳ 等待确认中... (${i + 1}/${maxAttempts})`);
    }

    console.log("⚠️ 交易确认超时，请手动检查交易状态");
    return false;
}

// 检查交易状态
async function checkTransactionStatus(signature) {
    try {
        const rpcEndpoint = 'https://api.mainnet-beta.solana.com';

        const response = await fetch(rpcEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 1,
                method: 'getSignatureStatuses',
                params: [
                    [signature],
                    {
                        searchTransactionHistory: true
                    }
                ]
            })
        });

        const result = await response.json();
        return result.result;

    } catch (error) {
        console.error("❌ 检查交易状态失败:", error);
        return null;
    }
}

// 定时器配置
const TIMER_CONFIG = {
    interval: 5000,        // 检查间隔：5秒
    maxAttempts: 100,      // 最大尝试次数：100次 (约8分钟)
    retryDelay: 2000       // 失败后重试延迟：2秒
};

// 全局变量跟踪状态
let timerState = {
    isRunning: false,
    attempts: 0,
    successCount: 0,
    intervalId: null,
    startTime: null
};

// 启动定时器交易
async function startTimerTrade() {
    if (timerState.isRunning) {
        console.log("⚠️ 定时器已在运行中...");
        return;
    }

    console.log("🚀 启动定时器交易模式");
    console.log(`⏰ 检查间隔: ${TIMER_CONFIG.interval/1000}秒`);
    console.log(`🔄 最大尝试次数: ${TIMER_CONFIG.maxAttempts}`);
    console.log("📝 输入 stopTimerTrade() 可停止定时器");

    timerState.isRunning = true;
    timerState.attempts = 0;
    timerState.successCount = 0;
    timerState.startTime = new Date();

    // 立即执行一次
    await executeTimerTrade();

    // 设置定时器
    timerState.intervalId = setInterval(async () => {
        if (timerState.attempts >= TIMER_CONFIG.maxAttempts) {
            console.log("🛑 达到最大尝试次数，停止定时器");
            stopTimerTrade();
            return;
        }

        await executeTimerTrade();
    }, TIMER_CONFIG.interval);
}

// 停止定时器交易
function stopTimerTrade() {
    if (!timerState.isRunning) {
        console.log("⚠️ 定时器未在运行");
        return;
    }

    if (timerState.intervalId) {
        clearInterval(timerState.intervalId);
        timerState.intervalId = null;
    }

    timerState.isRunning = false;

    const endTime = new Date();
    const duration = Math.round((endTime - timerState.startTime) / 1000);

    console.log("🛑 定时器已停止");
    console.log(`📊 统计信息:`);
    console.log(`   - 总尝试次数: ${timerState.attempts}`);
    console.log(`   - 成功交易数: ${timerState.successCount}`);
    console.log(`   - 运行时长: ${duration}秒`);
}

// 定时器执行的交易函数
async function executeTimerTrade() {
    if (!timerState.isRunning) return;

    timerState.attempts++;
    const currentTime = new Date().toLocaleTimeString();

    console.log(`\n🔄 [${currentTime}] 第 ${timerState.attempts} 次尝试...`);

    try {
        const success = await executeOrcaTrade();

        if (success) {
            timerState.successCount++;
            console.log(`✅ [${currentTime}] 交易成功! (总成功: ${timerState.successCount})`);

            // 可选：成功后是否停止定时器
            // stopTimerTrade();
        } else {
            console.log(`❌ [${currentTime}] 交易失败，等待下次重试...`);
        }

    } catch (error) {
        console.error(`❌ [${currentTime}] 执行出错:`, error.message);

        // 如果是网络错误，等待一段时间后继续
        if (error.message.includes('fetch') || error.message.includes('network')) {
            console.log(`⏳ 网络错误，${TIMER_CONFIG.retryDelay/1000}秒后继续...`);
            await new Promise(resolve => setTimeout(resolve, TIMER_CONFIG.retryDelay));
        }
    }
}

// 显示定时器状态
function showTimerStatus() {
    if (!timerState.isRunning) {
        console.log("⚠️ 定时器未运行");
        return;
    }

    const currentTime = new Date();
    const duration = Math.round((currentTime - timerState.startTime) / 1000);
    const nextCheck = Math.round((TIMER_CONFIG.interval - (Date.now() % TIMER_CONFIG.interval)) / 1000);

    console.log("📊 定时器状态:");
    console.log(`   - 运行状态: ✅ 运行中`);
    console.log(`   - 已尝试: ${timerState.attempts}/${TIMER_CONFIG.maxAttempts}`);
    console.log(`   - 成功次数: ${timerState.successCount}`);
    console.log(`   - 运行时长: ${duration}秒`);
    console.log(`   - 下次检查: ${nextCheck}秒后`);
}

// 修改定时器配置
function setTimerConfig(options = {}) {
    if (timerState.isRunning) {
        console.log("⚠️ 请先停止定时器再修改配置");
        return;
    }

    if (options.interval) {
        TIMER_CONFIG.interval = Math.max(1000, options.interval); // 最小1秒
        console.log(`✅ 检查间隔已设置为: ${TIMER_CONFIG.interval/1000}秒`);
    }

    if (options.maxAttempts) {
        TIMER_CONFIG.maxAttempts = Math.max(1, options.maxAttempts); // 最小1次
        console.log(`✅ 最大尝试次数已设置为: ${TIMER_CONFIG.maxAttempts}`);
    }

    if (options.retryDelay) {
        TIMER_CONFIG.retryDelay = Math.max(0, options.retryDelay); // 最小0秒
        console.log(`✅ 重试延迟已设置为: ${TIMER_CONFIG.retryDelay/1000}秒`);
    }

    console.log("📋 当前配置:", TIMER_CONFIG);
}

// 重置统计信息
function resetStats() {
    if (timerState.isRunning) {
        console.log("⚠️ 请先停止定时器再重置统计");
        return;
    }

    timerState.attempts = 0;
    timerState.successCount = 0;
    timerState.startTime = null;

    console.log("✅ 统计信息已重置");
}

// 快速启动命令
console.log("🎯 Orca定时交易脚本已加载!");
console.log("📝 可用命令:");
console.log("   startTimerTrade()     - 启动定时器交易");
console.log("   stopTimerTrade()      - 停止定时器交易");
console.log("   showTimerStatus()     - 查看定时器状态");
console.log("   executeOrcaTrade()    - 执行单次交易");
console.log("   setTimerConfig({...}) - 修改定时器配置");
console.log("   resetStats()          - 重置统计信息");
console.log("");
console.log("🔧 配置示例:");
console.log("   setTimerConfig({interval: 3000, maxAttempts: 50})");
console.log("   // 设置3秒间隔，最多尝试50次");
console.log("");
console.log("🚀 快速开始: startTimerTrade()");