async function executeOrcaTrade() {
    const siteKey = '6LfI-l8rAAAAANFOE5niDrO8PEvuqHfvddN-4SNw';
    // 钱包地址
    const trader = "Bk12DihjYXEyT64899d3ZMEUNPQ2YWngbNsL2KzyFVo2";
    // 买的USDC 数量 6位小数
    let amountIn = 10
    amountIn += "00000";

    // token 地址
    const address = "TUNAfXDZEdQizTMTh3uEvNvYqJmqFHZbEJt8joP4cyx";

    // 1. 获取recahptcha token
    let token;
    try {
        token = await grecaptcha.enterprise.execute(siteKey, { action: 'buy' });
        console.log("✅ Captcha token (rresp):", token);
    } catch (e) {
        console.error("❌ 获取 reCAPTCHA token 失败:", e);
        return;
    }

    // 2. 发起 Orca 交易请求
    const body = {
        trader,
        address,
        token,
        side: "buy",
        args: {
            type: "exactIn",
            amountIn,
            allowPartialFill: true,
            priceThreshold: {
                numerator: 100000000,
                denominator: "2671627902570772"
            }
        },
        feeConfig: {
            priorityFee: {
                type: "dynamic",
                parameters: {
                    percentile: 50,
                    maxLamports: 9996000
                }
            },
            jito: {
                type: "dynamic",
                parameters: {
                    percentile: 50,
                    maxLamports: 4000
                }
            }
        }
    };

    try {
        const response = await fetch("https://api.orca.so/v2/solana/wavebreak/trades", {
            method: "POST",
            headers: {
                accept: "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "cache-control": "no-cache",
                "content-type": "application/json",
                pragma: "no-cache",
                priority: "u=1, i",
                "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site"
            },
            referrer: "https://www.orca.so/",
            body: JSON.stringify(body),
            mode: "cors",
            credentials: "omit"
        });

        const data = await response.json();
        console.log("🎯 Orca 返回结果:", data);
    } catch (err) {
        console.error("❌ 请求失败:", err);
    }
}

executeOrcaTrade()