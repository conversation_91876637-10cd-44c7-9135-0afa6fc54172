/**
 * Orca交易自动化脚本 - 私钥签名版本
 *
 * 使用说明：
 * 1. 在下方配置你的私钥
 * 2. 在Orca网站上打开开发者工具
 * 3. 将此代码粘贴到控制台中运行
 * 4. 脚本会自动处理交易签名和广播
 *
 * 注意事项：
 * - 请确保账户中有足够的SOL支付交易费用
 * - 私钥信息请妥善保管，不要泄露
 * - 交易会自动等待确认，请耐心等待
 */

// ⚠️ 配置你的私钥 (请替换为你的实际私钥)
let PRIVATE_KEY = ""; // 在这里填入你的base58格式私钥字符串
// 钱包地址
const trader = "Bk12DihjYXEyT64899d3ZMEUNPQ2YWngbNsL2KzyFVo2";

// 示例：PRIVATE_KEY = "你的base58私钥字符串";

// 如果你有base58格式的私钥，可以使用这个函数转换
function base58ToBytes(base58) {
    const alphabet = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';
    let decoded = 0n;
    let multi = 1n;

    for (let i = base58.length - 1; i >= 0; i--) {
        const char = base58[i];
        const index = alphabet.indexOf(char);
        if (index === -1) throw new Error('Invalid base58 character');
        decoded += BigInt(index) * multi;
        multi *= 58n;
    }

    const bytes = [];
    while (decoded > 0) {
        bytes.unshift(Number(decoded % 256n));
        decoded = decoded / 256n;
    }

    // Handle leading zeros
    for (let i = 0; i < base58.length && base58[i] === '1'; i++) {
        bytes.unshift(0);
    }

    return new Uint8Array(bytes);
}

// 加载必要的加密库
async function loadSolanaLibs() {
    if (window.solanaWeb3) {
        return window.solanaWeb3;
    }

    try {
        // 动态加载 Solana Web3.js
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/@solana/web3.js@latest/lib/index.iife.min.js';
        document.head.appendChild(script);

        return new Promise((resolve, reject) => {
            script.onload = () => {
                if (window.solanaWeb3) {
                    console.log("✅ Solana Web3.js 库加载成功");
                    resolve(window.solanaWeb3);
                } else {
                    reject(new Error("Failed to load Solana Web3.js"));
                }
            };
            script.onerror = () => reject(new Error("Failed to load Solana Web3.js"));
        });
    } catch (error) {
        console.error("❌ 加载 Solana 库失败:", error);
        throw error;
    }
}

// Ed25519 签名函数 (简化版本，用于浏览器环境)
async function loadCryptoLibs() {
    if (window.nacl) {
        return window.nacl;
    }

    try {
        // 动态加载 TweetNaCl
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/tweetnacl@1.0.3/nacl-fast.min.js';
        document.head.appendChild(script);

        return new Promise((resolve, reject) => {
            script.onload = () => {
                if (window.nacl) {
                    console.log("✅ TweetNaCl 库加载成功");
                    resolve(window.nacl);
                } else {
                    reject(new Error("Failed to load TweetNaCl"));
                }
            };
            script.onerror = () => reject(new Error("Failed to load TweetNaCl"));
        });
    } catch (error) {
        console.error("❌ 加载加密库失败:", error);
        throw error;
    }
}

async function executeOrcaTrade() {
    const siteKey = '6LfI-l8rAAAAANFOE5niDrO8PEvuqHfvddN-4SNw';
    // 买的USDC 数量 6位小数
    let amountIn = 10
    amountIn += "00000";

    // token 地址
    const address = "TUNAfXDZEdQizTMTh3uEvNvYqJmqFHZbEJt8joP4cyx";

    // 1. 获取recahptcha token
    let token;
    try {
        token = await grecaptcha.enterprise.execute(siteKey, { action: 'buy' });
        console.log("✅ Captcha token (rresp):", token);
    } catch (e) {
        console.error("❌ 获取 reCAPTCHA token 失败:", e);
        return;
    }

    // 2. 发起 Orca 交易请求
    const body = {
        trader,
        address,
        token,
        side: "buy",
        args: {
            type: "exactIn",
            amountIn,
            allowPartialFill: true,
            priceThreshold: {
                numerator: 100000000,
                denominator: "2671627902570772"
            }
        },
        feeConfig: {
            priorityFee: {
                type: "dynamic",
                parameters: {
                    percentile: 50,
                    maxLamports: 9996000
                }
            },
            jito: {
                type: "dynamic",
                parameters: {
                    percentile: 50,
                    maxLamports: 4000
                }
            }
        }
    };

    try {
        const response = await fetch("https://api.orca.so/v2/solana/wavebreak/trades", {
            method: "POST",
            headers: {
                accept: "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "cache-control": "no-cache",
                "content-type": "application/json",
                pragma: "no-cache",
                priority: "u=1, i",
                "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site"
            },
            referrer: "https://www.orca.so/",
            body: JSON.stringify(body),
            mode: "cors",
            credentials: "omit"
        });

        const data = await response.json();
        // {"data":{"serializedTransaction": []}}
        console.log("🎯 Orca 返回结果:", data);

        // 处理返回的序列化交易
        if (data.data && data.data.serializedTransaction && data.data.serializedTransaction.length > 0) {
            console.log("📝 开始处理交易签名和广播...");
            const success = await signAndBroadcastTransaction(data.data.serializedTransaction);
            return success;
        } else {
            console.log("⚠️ 没有返回有效的交易数据");
            return false;
        }
    } catch (err) {
        console.error("❌ 请求失败:", err);
        return false;
    }
}

// 使用私钥签名和广播交易
async function signAndBroadcastTransaction(serializedTransactions) {
    let allSuccessful = true;

    try {
        // 检查私钥配置
        if (!PRIVATE_KEY || PRIVATE_KEY.trim() === "") {
            console.error("❌ 请先配置私钥 PRIVATE_KEY");
            console.log("💡 提示: 使用 setPrivateKey('你的私钥') 或直接在脚本顶部设置");
            return false;
        }

        // 加载必要的库
        console.log("📚 正在加载加密库...");
        const [solanaWeb3, nacl] = await Promise.all([
            loadSolanaLibs(),
            loadCryptoLibs()
        ]);

        // 将base58私钥转换为字节数组
        let privateKeyUint8;
        try {
            privateKeyUint8 = base58ToBytes(PRIVATE_KEY);
            if (privateKeyUint8.length !== 64) {
                console.error("❌ 私钥长度错误，应为64字节，当前长度:", privateKeyUint8.length);
                return false;
            }
        } catch (error) {
            console.error("❌ 私钥格式错误:", error.message);
            return false;
        }

        const keypair = nacl.sign.keyPair.fromSecretKey(privateKeyUint8);
        const publicKey = new solanaWeb3.PublicKey(keypair.publicKey);

        console.log("✅ 使用公钥:", publicKey.toString());

        // 处理每个交易
        for (let i = 0; i < serializedTransactions.length; i++) {
            const serializedTx = serializedTransactions[i];
            console.log(`📤 处理第 ${i + 1}/${serializedTransactions.length} 个交易...`);

            try {
                // 反序列化交易
                const transactionBuffer = new Uint8Array(
                    atob(serializedTx).split('').map(c => c.charCodeAt(0))
                );

                // 解析交易
                const transaction = solanaWeb3.Transaction.from(transactionBuffer);

                // 使用私钥签名交易
                const messageBytes = transaction.serializeMessage();
                const signatureBytes = nacl.sign.detached(messageBytes, privateKeyUint8);

                // 添加签名到交易
                transaction.addSignature(publicKey, signatureBytes);

                console.log("✅ 交易已签名");

                // 广播交易
                const txSignature = await broadcastTransaction(transaction.serialize());

                if (txSignature) {
                    console.log(`✅ 交易 ${i + 1} 发送成功!`);
                    console.log(`🔗 交易签名: ${txSignature}`);
                    console.log(`🌐 查看交易: https://solscan.io/tx/${txSignature}`);

                    // 等待交易确认
                    const confirmed = await waitForTransactionConfirmation(txSignature);
                    if (!confirmed) {
                        allSuccessful = false;
                    }
                } else {
                    console.error(`❌ 交易 ${i + 1} 发送失败`);
                    allSuccessful = false;
                }

            } catch (txError) {
                console.error(`❌ 处理交易 ${i + 1} 时出错:`, txError);
                allSuccessful = false;
            }
        }

        return allSuccessful;

    } catch (error) {
        console.error("❌ 签名交易失败:", error);

        if (error.message.includes('User rejected')) {
            console.log("ℹ️ 用户取消了交易签名");
        } else if (error.code === 4001) {
            console.log("ℹ️ 用户拒绝了连接请求");
        }

        return false;
    }
}

// 广播交易到Solana网络
async function broadcastTransaction(serializedTransaction) {
    try {
        const rpcEndpoint = 'https://api.mainnet-beta.solana.com';

        // 将序列化交易转换为base64
        const base64Transaction = btoa(String.fromCharCode(...serializedTransaction));

        const response = await fetch(rpcEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 1,
                method: 'sendTransaction',
                params: [
                    base64Transaction,
                    {
                        encoding: 'base64',
                        skipPreflight: false,
                        preflightCommitment: 'processed',
                        maxRetries: 3
                    }
                ]
            })
        });

        const result = await response.json();

        if (result.error) {
            console.error("❌ RPC错误:", result.error);
            return null;
        }

        return result.result;

    } catch (error) {
        console.error("❌ 广播交易失败:", error);
        return null;
    }
}

// 等待交易确认
async function waitForTransactionConfirmation(signature, maxAttempts = 30) {
    console.log("⏳ 等待交易确认...");

    for (let i = 0; i < maxAttempts; i++) {
        const status = await checkTransactionStatus(signature);

        if (status && status.value && status.value[0]) {
            const txStatus = status.value[0];

            if (txStatus.confirmationStatus === 'confirmed' || txStatus.confirmationStatus === 'finalized') {
                console.log("✅ 交易已确认!");
                return true;
            } else if (txStatus.err) {
                console.error("❌ 交易失败:", txStatus.err);
                return false;
            }
        }

        // 等待2秒后重试
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log(`⏳ 等待确认中... (${i + 1}/${maxAttempts})`);
    }

    console.log("⚠️ 交易确认超时，请手动检查交易状态");
    return false;
}

// 检查交易状态
async function checkTransactionStatus(signature) {
    try {
        const rpcEndpoint = 'https://api.mainnet-beta.solana.com';

        const response = await fetch(rpcEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 1,
                method: 'getSignatureStatuses',
                params: [
                    [signature],
                    {
                        searchTransactionHistory: true
                    }
                ]
            })
        });

        const result = await response.json();
        return result.result;

    } catch (error) {
        console.error("❌ 检查交易状态失败:", error);
        return null;
    }
}

// 定时器配置
const TIMER_CONFIG = {
    interval: 2000,        // 检查间隔：5秒
    maxAttempts: 100,      // 最大尝试次数：100次 (约8分钟)
    retryDelay: 2000       // 失败后重试延迟：2秒
};

// 全局变量跟踪状态
let timerState = {
    isRunning: false,
    attempts: 0,
    successCount: 0,
    intervalId: null,
    startTime: null
};

// 启动定时器交易
async function startTimerTrade() {
    if (timerState.isRunning) {
        console.log("⚠️ 定时器已在运行中...");
        return;
    }

    console.log("🚀 启动定时器交易模式");
    console.log(`⏰ 检查间隔: ${TIMER_CONFIG.interval / 1000}秒`);
    console.log(`🔄 最大尝试次数: ${TIMER_CONFIG.maxAttempts}`);
    console.log("📝 输入 stopTimerTrade() 可停止定时器");

    timerState.isRunning = true;
    timerState.attempts = 0;
    timerState.successCount = 0;
    timerState.startTime = new Date();

    // 立即执行一次
    await executeTimerTrade();

    // 设置定时器
    timerState.intervalId = setInterval(async () => {
        if (timerState.attempts >= TIMER_CONFIG.maxAttempts) {
            console.log("🛑 达到最大尝试次数，停止定时器");
            stopTimerTrade();
            return;
        }

        await executeTimerTrade();
    }, TIMER_CONFIG.interval);
}

// 停止定时器交易
function stopTimerTrade() {
    if (!timerState.isRunning) {
        console.log("⚠️ 定时器未在运行");
        return;
    }

    if (timerState.intervalId) {
        clearInterval(timerState.intervalId);
        timerState.intervalId = null;
    }

    timerState.isRunning = false;

    const endTime = new Date();
    const duration = Math.round((endTime - timerState.startTime) / 1000);

    console.log("🛑 定时器已停止");
    console.log(`📊 统计信息:`);
    console.log(`   - 总尝试次数: ${timerState.attempts}`);
    console.log(`   - 成功交易数: ${timerState.successCount}`);
    console.log(`   - 运行时长: ${duration}秒`);
}

// 定时器执行的交易函数
async function executeTimerTrade() {
    if (!timerState.isRunning) return;

    timerState.attempts++;
    const currentTime = new Date().toLocaleTimeString();

    console.log(`\n🔄 [${currentTime}] 第 ${timerState.attempts} 次尝试...`);

    try {
        const success = await executeOrcaTrade();

        if (success) {
            timerState.successCount++;
            console.log(`✅ [${currentTime}] 交易成功! (总成功: ${timerState.successCount})`);

            // 可选：成功后是否停止定时器
            // stopTimerTrade();
        } else {
            console.log(`❌ [${currentTime}] 交易失败，等待下次重试...`);
        }

    } catch (error) {
        console.error(`❌ [${currentTime}] 执行出错:`, error.message);

        // 如果是网络错误，等待一段时间后继续
        if (error.message.includes('fetch') || error.message.includes('network')) {
            console.log(`⏳ 网络错误，${TIMER_CONFIG.retryDelay / 1000}秒后继续...`);
            await new Promise(resolve => setTimeout(resolve, TIMER_CONFIG.retryDelay));
        }
    }
}

// 显示定时器状态
function showTimerStatus() {
    if (!timerState.isRunning) {
        console.log("⚠️ 定时器未运行");
        return;
    }

    const currentTime = new Date();
    const duration = Math.round((currentTime - timerState.startTime) / 1000);
    const nextCheck = Math.round((TIMER_CONFIG.interval - (Date.now() % TIMER_CONFIG.interval)) / 1000);

    console.log("📊 定时器状态:");
    console.log(`   - 运行状态: ✅ 运行中`);
    console.log(`   - 已尝试: ${timerState.attempts}/${TIMER_CONFIG.maxAttempts}`);
    console.log(`   - 成功次数: ${timerState.successCount}`);
    console.log(`   - 运行时长: ${duration}秒`);
    console.log(`   - 下次检查: ${nextCheck}秒后`);
}

// 修改定时器配置
function setTimerConfig(options = {}) {
    if (timerState.isRunning) {
        console.log("⚠️ 请先停止定时器再修改配置");
        return;
    }

    if (options.interval) {
        TIMER_CONFIG.interval = Math.max(1000, options.interval); // 最小1秒
        console.log(`✅ 检查间隔已设置为: ${TIMER_CONFIG.interval / 1000}秒`);
    }

    if (options.maxAttempts) {
        TIMER_CONFIG.maxAttempts = Math.max(1, options.maxAttempts); // 最小1次
        console.log(`✅ 最大尝试次数已设置为: ${TIMER_CONFIG.maxAttempts}`);
    }

    if (options.retryDelay) {
        TIMER_CONFIG.retryDelay = Math.max(0, options.retryDelay); // 最小0秒
        console.log(`✅ 重试延迟已设置为: ${TIMER_CONFIG.retryDelay / 1000}秒`);
    }

    console.log("📋 当前配置:", TIMER_CONFIG);
}

// 重置统计信息
function resetStats() {
    if (timerState.isRunning) {
        console.log("⚠️ 请先停止定时器再重置统计");
        return;
    }

    timerState.attempts = 0;
    timerState.successCount = 0;
    timerState.startTime = null;

    console.log("✅ 统计信息已重置");
}

// 辅助函数：设置私钥
function setPrivateKey(privateKeyString) {
    if (typeof privateKeyString !== 'string') {
        console.error("❌ 私钥应为字符串格式");
        return false;
    }

    if (privateKeyString.trim() === "") {
        console.error("❌ 私钥不能为空");
        return false;
    }

    // 验证私钥格式
    try {
        const bytes = base58ToBytes(privateKeyString);
        if (bytes.length !== 64) {
            console.error("❌ 私钥长度错误，应为64字节，当前长度:", bytes.length);
            return false;
        }

        PRIVATE_KEY = privateKeyString;
        console.log("✅ 私钥已设置");
        console.log("🔑 私钥长度验证通过");
        return true;
    } catch (error) {
        console.error("❌ 私钥格式错误:", error.message);
        return false;
    }
}

// 快速启动命令
console.log("🎯 Orca定时交易脚本已加载! (私钥签名版本)");
console.log("");
if (PRIVATE_KEY.trim() === "") {
    console.log("⚠️  重要：请先设置私钥!");
    console.log("📝 设置私钥:");
    console.log("   方法1: 直接修改脚本顶部的 PRIVATE_KEY 变量");
    console.log("   方法2: 使用命令 setPrivateKey('你的base58私钥')");
} else {
    console.log("✅ 私钥已配置，可以直接开始交易");
}
console.log("");
console.log("📝 可用命令:");
console.log("   setPrivateKey('key')  - 设置私钥");
console.log("   startTimerTrade()     - 启动定时器交易");
console.log("   stopTimerTrade()      - 停止定时器交易");
console.log("   showTimerStatus()     - 查看定时器状态");
console.log("   executeOrcaTrade()    - 执行单次交易");
console.log("   setTimerConfig({...}) - 修改定时器配置");
console.log("   resetStats()          - 重置统计信息");
console.log("");
console.log("🔧 配置示例:");
console.log("   setTimerConfig({interval: 3000, maxAttempts: 50})");
console.log("   // 设置3秒间隔，最多尝试50次");
console.log("");
console.log("🚀 使用流程:");
if (PRIVATE_KEY.trim() === "") {
    console.log("   1. setPrivateKey('你的私钥')");
    console.log("   2. startTimerTrade()");
} else {
    console.log("   直接运行: startTimerTrade()");
}