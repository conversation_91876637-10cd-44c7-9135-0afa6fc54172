/**
 * Orca交易自动化脚本 - 浏览器开发者工具版本
 *
 * 使用说明：
 * 1. 确保已安装Phantom钱包扩展
 * 2. 在Orca网站上打开开发者工具
 * 3. 将此代码粘贴到控制台中运行
 * 4. 脚本会自动处理交易签名和广播
 *
 * 注意事项：
 * - 请确保钱包中有足够的SOL支付交易费用
 * - 交易会自动等待确认，请耐心等待
 * - 如遇到问题，请检查钱包连接状态
 */

async function executeOrcaTrade() {
    const siteKey = '6LfI-l8rAAAAANFOE5niDrO8PEvuqHfvddN-4SNw';
    // 钱包地址
    const trader = "Bk12DihjYXEyT64899d3ZMEUNPQ2YWngbNsL2KzyFVo2";
    // 买的USDC 数量 6位小数
    let amountIn = 10
    amountIn += "00000";

    // token 地址
    const address = "TUNAfXDZEdQizTMTh3uEvNvYqJmqFHZbEJt8joP4cyx";

    // 1. 获取recahptcha token
    let token;
    try {
        token = await grecaptcha.enterprise.execute(siteKey, { action: 'buy' });
        console.log("✅ Captcha token (rresp):", token);
    } catch (e) {
        console.error("❌ 获取 reCAPTCHA token 失败:", e);
        return;
    }

    // 2. 发起 Orca 交易请求
    const body = {
        trader,
        address,
        token,
        side: "buy",
        args: {
            type: "exactIn",
            amountIn,
            allowPartialFill: true,
            priceThreshold: {
                numerator: 100000000,
                denominator: "2671627902570772"
            }
        },
        feeConfig: {
            priorityFee: {
                type: "dynamic",
                parameters: {
                    percentile: 50,
                    maxLamports: 9996000
                }
            },
            jito: {
                type: "dynamic",
                parameters: {
                    percentile: 50,
                    maxLamports: 4000
                }
            }
        }
    };

    try {
        const response = await fetch("https://api.orca.so/v2/solana/wavebreak/trades", {
            method: "POST",
            headers: {
                accept: "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "cache-control": "no-cache",
                "content-type": "application/json",
                pragma: "no-cache",
                priority: "u=1, i",
                "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"macOS\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-site"
            },
            referrer: "https://www.orca.so/",
            body: JSON.stringify(body),
            mode: "cors",
            credentials: "omit"
        });

        const data = await response.json();
        // {"data":{"serializedTransaction": []}}
        console.log("🎯 Orca 返回结果:", data);

        // 处理返回的序列化交易
        if (data.data && data.data.serializedTransaction && data.data.serializedTransaction.length > 0) {
            console.log("📝 开始处理交易签名和广播...");
            await signAndBroadcastTransaction(data.data.serializedTransaction);
        } else {
            console.log("⚠️ 没有返回有效的交易数据");
        }
    } catch (err) {
        console.error("❌ 请求失败:", err);
    }
}

// 在浏览器环境中签名和广播交易
async function signAndBroadcastTransaction(serializedTransactions) {
    try {
        // 检查是否有Solana钱包
        if (!window.solana || !window.solana.isPhantom) {
            console.error("❌ 未检测到Phantom钱包，请安装Phantom钱包扩展");
            return;
        }

        // 连接钱包
        const wallet = window.solana;
        if (!wallet.isConnected) {
            console.log("🔗 正在连接钱包...");
            await wallet.connect();
        }

        console.log("✅ 钱包已连接:", wallet.publicKey.toString());

        // 处理每个交易
        for (let i = 0; i < serializedTransactions.length; i++) {
            const serializedTx = serializedTransactions[i];
            console.log(`📤 处理第 ${i + 1}/${serializedTransactions.length} 个交易...`);

            try {
                // 使用钱包签名并发送交易
                const response = await wallet.signAndSendTransaction({
                    message: serializedTx
                });

                console.log("✅ 交易已签名并发送");
                const signature = response.signature;

                if (signature) {
                    console.log(`✅ 交易 ${i + 1} 发送成功!`);
                    console.log(`🔗 交易签名: ${signature}`);
                    console.log(`🌐 查看交易: https://solscan.io/tx/${signature}`);

                    // 等待交易确认
                    await waitForTransactionConfirmation(signature);
                } else {
                    console.error(`❌ 交易 ${i + 1} 发送失败`);
                }

            } catch (txError) {
                console.error(`❌ 处理交易 ${i + 1} 时出错:`, txError);
            }
        }

    } catch (error) {
        console.error("❌ 签名交易失败:", error);

        if (error.message.includes('User rejected')) {
            console.log("ℹ️ 用户取消了交易签名");
        } else if (error.code === 4001) {
            console.log("ℹ️ 用户拒绝了连接请求");
        }
    }
}

// 等待交易确认
async function waitForTransactionConfirmation(signature, maxAttempts = 30) {
    console.log("⏳ 等待交易确认...");

    for (let i = 0; i < maxAttempts; i++) {
        const status = await checkTransactionStatus(signature);

        if (status && status.value && status.value[0]) {
            const txStatus = status.value[0];

            if (txStatus.confirmationStatus === 'confirmed' || txStatus.confirmationStatus === 'finalized') {
                console.log("✅ 交易已确认!");
                return true;
            } else if (txStatus.err) {
                console.error("❌ 交易失败:", txStatus.err);
                return false;
            }
        }

        // 等待2秒后重试
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log(`⏳ 等待确认中... (${i + 1}/${maxAttempts})`);
    }

    console.log("⚠️ 交易确认超时，请手动检查交易状态");
    return false;
}

// 检查交易状态
async function checkTransactionStatus(signature) {
    try {
        const rpcEndpoint = 'https://api.mainnet-beta.solana.com';

        const response = await fetch(rpcEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                jsonrpc: '2.0',
                id: 1,
                method: 'getSignatureStatuses',
                params: [
                    [signature],
                    {
                        searchTransactionHistory: true
                    }
                ]
            })
        });

        const result = await response.json();
        return result.result;

    } catch (error) {
        console.error("❌ 检查交易状态失败:", error);
        return null;
    }
}

executeOrcaTrade()